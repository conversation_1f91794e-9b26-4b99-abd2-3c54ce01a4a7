import nodemailer from 'nodemailer'
import { createTransport } from 'nodemailer'

// Email templates
const emailTemplates = {
  magicLink: {
    subject: 'Sign in to PROTEC Alumni Network',
    html: (url: string, host: string) => `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sign in to PROTEC Alumni Network</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
          }
          .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
          }
          .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #012A5B, #C41E3A);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
          }
          .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #012A5B;
          }
          .title {
            color: #012A5B;
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
          }
          .subtitle {
            color: #64748b;
            font-size: 16px;
            margin: 0 0 30px 0;
          }
          .button {
            display: inline-block;
            background: linear-gradient(135deg, #C41E3A, #012A5B);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            transition: transform 0.2s;
          }
          .button:hover {
            transform: translateY(-2px);
          }
          .link-fallback {
            background-color: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 14px;
            color: #475569;
          }
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
          }
          .security-note {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            color: #92400e;
            font-size: 14px;
          }
          .security-note strong {
            color: #78350f;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">
              <div class="logo-icon">P</div>
              <div class="logo-text">PROTEC Alumni</div>
            </div>
            <h1 class="title">Welcome back!</h1>
            <p class="subtitle">Click the button below to sign in to your account</p>
          </div>

          <div style="text-align: center;">
            <a href="${url}" class="button">Sign in to PROTEC Alumni</a>
          </div>

          <div class="security-note">
            <strong>Security Notice:</strong> This link will expire in 24 hours and can only be used once. 
            If you didn't request this email, you can safely ignore it.
          </div>

          <div class="link-fallback">
            <p style="margin: 0 0 8px 0; font-weight: 600; color: #374151;">
              If the button doesn't work, copy and paste this link:
            </p>
            ${url}
          </div>

          <div class="footer">
            <p>This email was sent to you because you requested to sign in to the PROTEC Alumni Network.</p>
            <p>If you have any questions, contact us at <a href="mailto:<EMAIL>" style="color: #C41E3A;"><EMAIL></a></p>
            <p style="margin-top: 20px;">
              <strong>PROTEC Alumni Network</strong><br>
              Building careers in STEM since 1982
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (url: string, host: string) => `
      Sign in to PROTEC Alumni Network

      Welcome back! Click the link below to sign in to your account:

      ${url}

      This link will expire in 24 hours and can only be used once.

      If you didn't request this email, you can safely ignore it.

      If you have any questions, contact <NAME_EMAIL>

      PROTEC Alumni Network
      Building careers in STEM since 1982
    `
  },
  
  welcome: {
    subject: 'Welcome to PROTEC Alumni Network!',
    html: (name: string) => `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to PROTEC Alumni Network</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
          }
          .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .logo {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
          }
          .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #012A5B, #C41E3A);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
          }
          .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #012A5B;
          }
          .title {
            color: #012A5B;
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
          }
          .subtitle {
            color: #64748b;
            font-size: 16px;
            margin: 0 0 30px 0;
          }
          .button {
            display: inline-block;
            background: linear-gradient(135deg, #C41E3A, #012A5B);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
          }
          .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
          }
          .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 12px;
          }
          .feature-list li:last-child {
            border-bottom: none;
          }
          .feature-icon {
            width: 24px;
            height: 24px;
            background: #C41E3A;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
          }
          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 14px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="logo">
              <div class="logo-icon">P</div>
              <div class="logo-text">PROTEC Alumni</div>
            </div>
            <h1 class="title">Welcome to the network, ${name}!</h1>
            <p class="subtitle">You're now part of a community of 15,000+ PROTEC alumni worldwide</p>
          </div>

          <p>Congratulations on joining the PROTEC Alumni Network! Here's what you can do now:</p>

          <ul class="feature-list">
            <li>
              <div class="feature-icon">👥</div>
              <div>
                <strong>Connect with Alumni:</strong> Find and connect with fellow PROTEC graduates in your field or location
              </div>
            </li>
            <li>
              <div class="feature-icon">📅</div>
              <div>
                <strong>Join Events:</strong> Attend networking events, workshops, and professional development sessions
              </div>
            </li>
            <li>
              <div class="feature-icon">💼</div>
              <div>
                <strong>Discover Opportunities:</strong> Access job postings and career opportunities shared by the community
              </div>
            </li>
            <li>
              <div class="feature-icon">🎓</div>
              <div>
                <strong>Give Back:</strong> Mentor current students and recent graduates
              </div>
            </li>
          </ul>

          <div style="text-align: center;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" class="button">Complete Your Profile</a>
          </div>

          <div class="footer">
            <p>Need help getting started? Contact us at <a href="mailto:<EMAIL>" style="color: #C41E3A;"><EMAIL></a></p>
            <p style="margin-top: 20px;">
              <strong>PROTEC Alumni Network</strong><br>
              Building careers in STEM since 1982
            </p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: (name: string) => `
      Welcome to PROTEC Alumni Network!

      Congratulations ${name}! You're now part of a community of 15,000+ PROTEC alumni worldwide.

      Here's what you can do now:
      
      • Connect with Alumni: Find and connect with fellow PROTEC graduates
      • Join Events: Attend networking events and professional development sessions  
      • Discover Opportunities: Access job postings and career opportunities
      • Give Back: Mentor current students and recent graduates

      Complete your profile: ${process.env.NEXTAUTH_URL}/dashboard

      Need help? Contact <NAME_EMAIL>

      PROTEC Alumni Network
      Building careers in STEM since 1982
    `
  }
}

// Create email transporter
function createEmailTransporter() {
  const config = {
    host: process.env.EMAIL_SERVER_HOST,
    port: Number(process.env.EMAIL_SERVER_PORT) || 587,
    secure: process.env.EMAIL_SERVER_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
    // Additional configuration for better deliverability
    pool: true,
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 1000,
    rateLimit: 5,
  }

  return createTransport(config)
}

// Email service functions
export const emailService = {
  // Send magic link email
  async sendMagicLink(email: string, url: string, host: string) {
    const transporter = createEmailTransporter()
    
    try {
      const info = await transporter.sendMail({
        from: {
          name: 'PROTEC Alumni Network',
          address: process.env.EMAIL_FROM || '<EMAIL>'
        },
        to: email,
        subject: emailTemplates.magicLink.subject,
        html: emailTemplates.magicLink.html(url, host),
        text: emailTemplates.magicLink.text(url, host),
        headers: {
          'X-Priority': '1',
          'X-MSMail-Priority': 'High',
          'Importance': 'high'
        }
      })

      console.log('Magic link email sent:', info.messageId)
      return { success: true, messageId: info.messageId }
    } catch (error) {
      console.error('Failed to send magic link email:', error)
      throw new Error('Failed to send magic link email')
    } finally {
      transporter.close()
    }
  },

  // Send welcome email
  async sendWelcomeEmail(email: string, name: string) {
    const transporter = createEmailTransporter()
    
    try {
      const info = await transporter.sendMail({
        from: {
          name: 'PROTEC Alumni Network',
          address: process.env.EMAIL_FROM || '<EMAIL>'
        },
        to: email,
        subject: emailTemplates.welcome.subject,
        html: emailTemplates.welcome.html(name),
        text: emailTemplates.welcome.text(name),
      })

      console.log('Welcome email sent:', info.messageId)
      return { success: true, messageId: info.messageId }
    } catch (error) {
      console.error('Failed to send welcome email:', error)
      throw new Error('Failed to send welcome email')
    } finally {
      transporter.close()
    }
  },

  // Test email configuration
  async testConnection() {
    const transporter = createEmailTransporter()
    
    try {
      await transporter.verify()
      console.log('Email server connection verified')
      return { success: true, message: 'Email server connection verified' }
    } catch (error) {
      console.error('Email server connection failed:', error)
      throw new Error('Email server connection failed')
    } finally {
      transporter.close()
    }
  },

  // Send custom email
  async sendCustomEmail(
    to: string, 
    subject: string, 
    html: string, 
    text?: string,
    options?: {
      from?: string
      replyTo?: string
      cc?: string[]
      bcc?: string[]
    }
  ) {
    const transporter = createEmailTransporter()
    
    try {
      const info = await transporter.sendMail({
        from: options?.from || {
          name: 'PROTEC Alumni Network',
          address: process.env.EMAIL_FROM || '<EMAIL>'
        },
        to,
        subject,
        html,
        text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML if no text provided
        replyTo: options?.replyTo,
        cc: options?.cc,
        bcc: options?.bcc,
      })

      console.log('Custom email sent:', info.messageId)
      return { success: true, messageId: info.messageId }
    } catch (error) {
      console.error('Failed to send custom email:', error)
      throw new Error('Failed to send custom email')
    } finally {
      transporter.close()
    }
  }
}

// Export email templates for reuse
export { emailTemplates }