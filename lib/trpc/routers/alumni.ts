import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../server'
import { TRPCError } from '@trpc/server'
import { recommendationEngine } from '@/lib/services/recommendation-engine'

// Input validation schemas
const updateAlumniSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  bio: z.string().max(500).optional(),
  photoUrl: z.string().url().optional(),
  graduationYear: z.number().int().min(1982).max(new Date().getFullYear()).optional(),
  programmes: z.array(z.string()).optional(),
  currentRole: z.string().max(100).optional(),
  skills: z.array(z.string()).optional(),
  province: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  socialLinks: z.object({
    linkedin: z.string().url().optional(),
    twitter: z.string().url().optional(),
    github: z.string().url().optional(),
    website: z.string().url().optional(),
  }).optional(),
  privacy: z.object({
    showEmail: z.boolean().optional(),
    showPhone: z.boolean().optional(),
    showLocation: z.boolean().optional(),
    showConnections: z.boolean().optional(),
  }).optional(),
})

const searchAlumniSchema = z.object({
  query: z.string().optional(),
  graduationYear: z.number().int().min(1982).max(new Date().getFullYear()).optional(),
  programme: z.string().optional(),
  province: z.string().optional(),
  city: z.string().optional(),
  skills: z.array(z.string()).optional(),
  limit: z.number().int().min(1).max(100).default(20),
  cursor: z.string().optional(),
})

const connectAlumniSchema = z.object({
  alumniId: z.string().uuid(),
  message: z.string().max(500).optional(),
})

const connectionRequestSchema = z.object({
  requestId: z.string().uuid(),
  action: z.enum(['accept', 'decline']),
})

const enhancedSearchSchema = z.object({
  query: z.string().optional(),
  graduationYearRange: z.object({
    from: z.number().int().min(1982).optional(),
    to: z.number().int().max(new Date().getFullYear()).optional(),
  }).optional(),
  programmes: z.array(z.string()).optional(),
  industries: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  locations: z.object({
    provinces: z.array(z.string()).optional(),
    cities: z.array(z.string()).optional(),
    countries: z.array(z.string()).optional(),
  }).optional(),
  careerLevel: z.array(z.string()).optional(),
  companySize: z.array(z.string()).optional(),
  interests: z.array(z.string()).optional(),
  availability: z.object({
    mentoring: z.boolean().optional(),
    networking: z.boolean().optional(),
    jobOpportunities: z.boolean().optional(),
  }).optional(),
  sortBy: z.enum(['name', 'graduationYear', 'connections', 'lastLogin']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  limit: z.number().int().min(1).max(100).default(20),
  cursor: z.string().optional(),
})

export const alumniRouter = createTRPCRouter({
  // Get current user's profile
  me: protectedProcedure.query(async ({ ctx }) => {
    const alumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
      include: {
        connections: {
          select: {
            id: true,
            name: true,
            photoUrl: true,
            currentRole: true,
            graduationYear: true,
          },
        },
        _count: {
          select: {
            connections: true,
            posts: true,
            donations: true,
          },
        },
      },
    })

    if (!alumni) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Alumni profile not found',
      })
    }

    return alumni
  }),

  // Get alumni by ID
  getById: publicProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { id: input.id },
        include: {
          connections: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          posts: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              content: true,
              createdAt: true,
              likes: true,
              _count: {
                select: { comments: true },
              },
            },
          },
          _count: {
            select: {
              connections: true,
              posts: true,
              donations: true,
            },
          },
        },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni not found',
        })
      }

      // Filter sensitive information based on privacy settings
      const privacy = alumni.privacy as any
      if (!privacy?.showEmail) {
        alumni.email = ''
      }

      return alumni
    }),

  // Update current user's profile
  update: protectedProcedure
    .input(updateAlumniSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: input,
      })

      return updatedAlumni
    }),

  // Search alumni
  search: publicProcedure
    .input(searchAlumniSchema)
    .query(async ({ ctx, input }) => {
      const { query, graduationYear, programme, province, city, skills, limit, cursor } = input

      const where: any = {}

      if (query) {
        where.OR = [
          { name: { contains: query, mode: 'insensitive' } },
          { currentRole: { contains: query, mode: 'insensitive' } },
          { bio: { contains: query, mode: 'insensitive' } },
        ]
      }

      if (graduationYear) {
        where.graduationYear = graduationYear
      }

      if (programme) {
        where.programmes = { has: programme }
      }

      if (province) {
        where.province = { contains: province, mode: 'insensitive' }
      }

      if (city) {
        where.city = { contains: city, mode: 'insensitive' }
      }

      if (skills && skills.length > 0) {
        where.skills = { hasSome: skills }
      }

      const alumni = await ctx.prisma.alumni.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { name: 'asc' },
        select: {
          id: true,
          name: true,
          photoUrl: true,
          bio: true,
          graduationYear: true,
          programmes: true,
          currentRole: true,
          skills: true,
          province: true,
          city: true,
          _count: {
            select: {
              connections: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (alumni.length > limit) {
        const nextItem = alumni.pop()
        nextCursor = nextItem!.id
      }

      return {
        alumni,
        nextCursor,
      }
    }),

  // Connect with another alumni
  connect: protectedProcedure
    .input(connectAlumniSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      if (currentAlumni.id === input.alumniId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot connect with yourself',
        })
      }

      // Check if already connected
      const existingConnection = await ctx.prisma.alumni.findFirst({
        where: {
          id: currentAlumni.id,
          connections: {
            some: { id: input.alumniId },
          },
        },
      })

      if (existingConnection) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Already connected with this alumni',
        })
      }

      // Create bidirectional connection
      await ctx.prisma.alumni.update({
        where: { id: currentAlumni.id },
        data: {
          connections: {
            connect: { id: input.alumniId },
          },
        },
      })

      await ctx.prisma.alumni.update({
        where: { id: input.alumniId },
        data: {
          connections: {
            connect: { id: currentAlumni.id },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'CONNECTION',
          refId: input.alumniId,
          alumniId: currentAlumni.id,
        },
      })

      return { success: true }
    }),

  // Disconnect from another alumni
  disconnect: protectedProcedure
    .input(connectAlumniSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Remove bidirectional connection
      await ctx.prisma.alumni.update({
        where: { id: currentAlumni.id },
        data: {
          connections: {
            disconnect: { id: input.alumniId },
          },
        },
      })

      await ctx.prisma.alumni.update({
        where: { id: input.alumniId },
        data: {
          connections: {
            disconnect: { id: currentAlumni.id },
          },
        },
      })

      return { success: true }
    }),

  // Send connection request
  sendConnectionRequest: protectedProcedure
    .input(connectAlumniSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      if (currentAlumni.id === input.alumniId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot connect with yourself',
        })
      }

      // Check if already connected
      const existingConnection = await ctx.prisma.alumni.findFirst({
        where: {
          id: currentAlumni.id,
          connections: {
            some: { id: input.alumniId },
          },
        },
      })

      if (existingConnection) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Already connected with this alumni',
        })
      }

      // Check if request already exists
      const existingRequest = await ctx.prisma.connectionRequest.findFirst({
        where: {
          senderId: currentAlumni.id,
          receiverId: input.alumniId,
          status: 'pending'
        }
      })

      if (existingRequest) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Connection request already sent',
        })
      }

      // Create connection request
      const connectionRequest = await ctx.prisma.connectionRequest.create({
        data: {
          senderId: currentAlumni.id,
          receiverId: input.alumniId,
          message: input.message,
          status: 'pending'
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true
            }
          }
        }
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'CONNECTION_REQUEST_SENT',
          refId: connectionRequest.id,
          alumniId: currentAlumni.id,
        },
      })

      return connectionRequest
    }),

  // Handle connection request (accept/decline)
  handleConnectionRequest: protectedProcedure
    .input(connectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Find the connection request
      const connectionRequest = await ctx.prisma.connectionRequest.findUnique({
        where: { id: input.requestId },
        include: {
          sender: true,
          receiver: true
        }
      })

      if (!connectionRequest) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Connection request not found',
        })
      }

      if (connectionRequest.receiverId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Not authorized to handle this request',
        })
      }

      if (connectionRequest.status !== 'pending') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Request has already been handled',
        })
      }

      // Update request status
      await ctx.prisma.connectionRequest.update({
        where: { id: input.requestId },
        data: {
          status: input.action === 'accept' ? 'accepted' : 'declined',
          respondedAt: new Date()
        }
      })

      // If accepted, create bidirectional connection
      if (input.action === 'accept') {
        await ctx.prisma.alumni.update({
          where: { id: currentAlumni.id },
          data: {
            connections: {
              connect: { id: connectionRequest.senderId },
            },
          },
        })

        await ctx.prisma.alumni.update({
          where: { id: connectionRequest.senderId },
          data: {
            connections: {
              connect: { id: currentAlumni.id },
            },
          },
        })

        // Log activity for both users
        await ctx.prisma.activity.createMany({
          data: [
            {
              type: 'CONNECTION_ACCEPTED',
              refId: connectionRequest.senderId,
              alumniId: currentAlumni.id,
            },
            {
              type: 'CONNECTION_MADE',
              refId: currentAlumni.id,
              alumniId: connectionRequest.senderId,
            }
          ]
        })
      }

      return { success: true, action: input.action }
    }),

  // Get personalized recommendations
  getRecommendations: protectedProcedure
    .input(z.object({
      limit: z.number().int().min(1).max(50).default(10),
      excludeConnected: z.boolean().default(true),
      minScore: z.number().min(0).max(1).default(0.3),
      focusAreas: z.array(z.enum(['skills', 'industry', 'location', 'interests', 'career'])).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const recommendations = await recommendationEngine.generateRecommendations({
        userId: currentAlumni.id,
        ...input
      })

      // Get full alumni details for recommendations
      const alumniIds = recommendations.map(r => r.alumniId)
      const alumni = await ctx.prisma.alumni.findMany({
        where: { id: { in: alumniIds } },
        select: {
          id: true,
          name: true,
          photoUrl: true,
          bio: true,
          graduationYear: true,
          programmes: true,
          currentRole: true,
          company: true,
          industry: true,
          skills: true,
          interests: true,
          province: true,
          city: true,
          country: true,
          _count: {
            select: {
              connections: true,
            },
          },
        }
      })

      // Merge recommendations with alumni data
      return recommendations.map(rec => ({
        ...rec,
        alumni: alumni.find(a => a.id === rec.alumniId)
      })).filter(r => r.alumni) // Filter out any missing alumni
    }),

  // Get trending alumni
  getTrending: publicProcedure
    .input(z.object({
      limit: z.number().int().min(1).max(20).default(10)
    }))
    .query(async ({ ctx, input }) => {
      return await recommendationEngine.getTrendingAlumni(input.limit)
    }),

  // Get career path recommendations
  getCareerPathRecommendations: protectedProcedure
    .input(z.object({
      limit: z.number().int().min(1).max(10).default(5)
    }))
    .query(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      return await recommendationEngine.getCareerPathRecommendations(
        currentAlumni.id,
        input.limit
      )
    }),

  // Get alumni statistics
  getStats: publicProcedure.query(async ({ ctx }) => {
    const [totalAlumni, totalConnections, recentGraduates] = await Promise.all([
      ctx.prisma.alumni.count(),
      ctx.prisma.alumni.aggregate({
        _sum: {
          connections: true,
        },
      }),
      ctx.prisma.alumni.count({
        where: {
          graduationYear: {
            gte: new Date().getFullYear() - 5,
          },
        },
      }),
    ])

    return {
      totalAlumni,
      totalConnections: totalConnections._sum.connections || 0,
      recentGraduates,
    }
  }),
})
