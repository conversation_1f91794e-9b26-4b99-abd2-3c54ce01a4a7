generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User roles enum
enum UserRole {
  ALUMNI
  ADMIN
  EVENT_ORGANIZER
  DONOR_COORDINATOR
}

// Message status enum
enum MessageStatus {
  SENT
  DELIVERED
  READ
}

// Donation frequency enum
enum DonationFrequency {
  ONE_TIME
  MONTHLY
  QUARTERLY
  ANNUALLY
}

model Alumni {
  id             String    @id @default(uuid())
  email          String    @unique
  name           String
  photoUrl       String?
  bio            String?
  graduationYear Int
  programmes     String[]
  currentRole    String?
  company        String?
  industry       String?
  skills         String[]
  interests      String[]
  province       String
  city           String
  country        String    @default("South Africa")
  socialLinks    Json?
  privacy        Json
  role           UserRole  @default(ALUMNI)
  isActive       Boolean   @default(true)
  lastLoginAt    DateTime?

  // Enhanced profile fields
  careerHistory  CareerHistory[]
  education      Education[]
  protecInvolvement ProtecInvolvement[]

  // Relationships
  connections    Alumni[]  @relation("Connections")
  connectedWith  Alumni[]  @relation("Connections")
  activityLog    Activity[]
  posts          Post[]
  comments       Comment[]
  donations      Donation[]
  rsvps          RSVP[]
  organizedEvents Event[]

  // Messaging
  sentMessages     Message[] @relation("MessageSender")
  receivedMessages Message[] @relation("MessageReceiver")
  conversations    ConversationParticipant[]

  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

// Enhanced profile models
model CareerHistory {
  id          String   @id @default(uuid())
  alumni      Alumni   @relation(fields: [alumniId], references: [id], onDelete: Cascade)
  alumniId    String
  company     String
  position    String
  industry    String?
  startDate   DateTime
  endDate     DateTime?
  isCurrent   Boolean  @default(false)
  description String?
  location    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Education {
  id           String   @id @default(uuid())
  alumni       Alumni   @relation(fields: [alumniId], references: [id], onDelete: Cascade)
  alumniId     String
  institution  String
  degree       String
  fieldOfStudy String?
  startDate    DateTime
  endDate      DateTime?
  isCurrent    Boolean  @default(false)
  grade        String?
  description  String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model ProtecInvolvement {
  id          String   @id @default(uuid())
  alumni      Alumni   @relation(fields: [alumniId], references: [id], onDelete: Cascade)
  alumniId    String
  programme   String
  year        Int
  role        String?
  description String?
  achievements String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String
  category    String
  startTime   DateTime
  endTime     DateTime
  location    Json
  organizer   Alumni   @relation(fields: [organizerId], references: [id])
  organizerId String
  isPublic    Boolean  @default(true)
  maxAttendees Int?
  tags        String[]
  imageUrl    String?
  rsvps       RSVP[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Post {
  id        String    @id @default(uuid())
  author    Alumni    @relation(fields: [authorId], references: [id])
  authorId  String
  content   String
  mediaUrls String[]
  tags      String[]
  likes     String[]  // list of Alumni.id
  comments  Comment[]
  isModerated Boolean @default(false)
  moderatedBy String?
  moderatedAt DateTime?
  isPublic    Boolean @default(true)
  isPinned    Boolean @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Donation {
  id            String            @id @default(uuid())
  alumni        Alumni            @relation(fields: [alumniId], references: [id])
  alumniId      String
  amountZAR     Float
  gateway       String
  transactionId String
  status        String
  frequency     DonationFrequency @default(ONE_TIME)
  purpose       String            @default("general")
  isRecurring   Boolean           @default(false)
  nextPaymentDate DateTime?
  failedAttempts Int              @default(0)
  cancelledAt   DateTime?
  metadata      Json?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
}

model Activity {
  id        String   @id @default(uuid())
  type      String
  refId     String
  timestamp DateTime @default(now())
  alumni    Alumni   @relation(fields: [alumniId], references: [id])
  alumniId  String
}

model RSVP {
  id        String   @id @default(uuid())
  status    String
  responded DateTime @default(now())
  alumni    Alumni   @relation(fields: [alumniId], references: [id])
  alumniId  String
  event     Event    @relation(fields: [eventId], references: [id])
  eventId   String
}

model Comment {
  id        String   @id @default(uuid())
  text      String
  author    Alumni   @relation(fields: [authorId], references: [id])
  authorId  String
  post      Post     @relation(fields: [postId], references: [id])
  postId    String
  isModerated Boolean @default(false)
  moderatedBy String?
  moderatedAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Messaging System Models
model Conversation {
  id           String                    @id @default(uuid())
  title        String?
  isGroup      Boolean                   @default(false)
  participants ConversationParticipant[]
  messages     Message[]
  lastMessage  String?
  lastMessageAt DateTime?
  createdAt    DateTime                  @default(now())
  updatedAt    DateTime                  @updatedAt
}

model ConversationParticipant {
  id             String       @id @default(uuid())
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  conversationId String
  alumni         Alumni       @relation(fields: [alumniId], references: [id], onDelete: Cascade)
  alumniId       String
  joinedAt       DateTime     @default(now())
  lastReadAt     DateTime?
  isActive       Boolean      @default(true)

  @@unique([conversationId, alumniId])
}

model Message {
  id             String        @id @default(uuid())
  conversation   Conversation  @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  conversationId String
  sender         Alumni        @relation("MessageSender", fields: [senderId], references: [id])
  senderId       String
  receiver       Alumni?       @relation("MessageReceiver", fields: [receiverId], references: [id])
  receiverId     String?
  content        String
  messageType    String        @default("text") // text, image, file
  attachmentUrl  String?
  status         MessageStatus @default(SENT)
  readAt         DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

model SecurityLog {
  id        String   @id @default(uuid())
  type      String   // PAYMENT_ATTEMPT, PAYMENT_SUCCESS, PAYMENT_FAILURE, etc.
  userId    String?  // Email or user ID
  ipAddress String
  userAgent String
  metadata  Json?
  severity  String   // LOW, MEDIUM, HIGH, CRITICAL
  timestamp DateTime @default(now())

  @@index([userId, timestamp])
  @@index([type, timestamp])
  @@index([severity, timestamp])
}
