"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Mail, 
  CheckCircle, 
  Clock, 
  RefreshCw, 
  ArrowLeft,
  Shield,
  Smartphone,
  AlertTriangle
} from "lucide-react"
import { signIn } from "next-auth/react"
import { toast } from "sonner"

export default function VerifyRequestPage() {
  const searchParams = useSearchParams()
  const [isResending, setIsResending] = useState(false)
  const [resendCount, setResendCount] = useState(0)
  const [timeLeft, setTimeLeft] = useState(0)
  
  const email = searchParams.get("email") || ""
  const provider = searchParams.get("provider") || "email"
  const error = searchParams.get("error")

  // Cooldown timer for resend button
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [timeLeft])

  const handleResendEmail = async () => {
    if (!email || timeLeft > 0) return
    
    setIsResending(true)
    try {
      const result = await signIn("email", {
        email,
        redirect: false,
      })

      if (result?.error) {
        toast.error("Failed to resend email. Please try again.")
      } else {
        toast.success("Email sent! Check your inbox.")
        setResendCount(prev => prev + 1)
        setTimeLeft(60) // 60 second cooldown
      }
    } catch (error) {
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsResending(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-protec-gray via-white to-blue-50/30 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <Link href="/" className="inline-flex items-center space-x-2">
            <div className="flex h-10 w-10 items-center justify-center rounded-md bg-protec-navy">
              <span className="text-lg font-bold text-white">P</span>
            </div>
            <span className="text-2xl font-bold text-protec-navy">PROTEC Alumni</span>
          </Link>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-protec-navy/10">
              {error ? (
                <AlertTriangle className="h-8 w-8 text-red-500" />
              ) : (
                <Mail className="h-8 w-8 text-protec-navy" />
              )}
            </div>
            
            <div>
              <CardTitle className="text-2xl text-protec-navy">
                {error ? "Email Error" : "Check your email"}
              </CardTitle>
              <CardDescription className="mt-2">
                {error 
                  ? "There was a problem sending your sign-in email"
                  : "We've sent a magic link to your email address"
                }
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {error === "EmailSignin" 
                    ? "Failed to send the email. Please check your email address and try again."
                    : "An error occurred while sending the email. Please try again."
                  }
                </AlertDescription>
              </Alert>
            )}

            {!error && email && (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-2">
                    We sent a sign-in link to:
                  </p>
                  <p className="font-medium text-protec-navy bg-protec-gray/20 rounded-lg px-3 py-2 break-all">
                    {email}
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start gap-3 text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Click the link in the email to sign in instantly</span>
                  </div>
                  <div className="flex items-start gap-3 text-sm text-gray-600">
                    <Clock className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span>The link will expire in 24 hours for security</span>
                  </div>
                  <div className="flex items-start gap-3 text-sm text-gray-600">
                    <Shield className="h-4 w-4 text-protec-navy mt-0.5 flex-shrink-0" />
                    <span>The link can only be used once</span>
                  </div>
                </div>

                {/* Mobile app tip */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Smartphone className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-900 mb-1">
                        Using a mobile device?
                      </p>
                      <p className="text-blue-700">
                        Make sure to open the email link in the same browser where you requested the sign-in.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Resend section */}
                <div className="pt-4 border-t border-gray-100">
                  <div className="text-center space-y-3">
                    <p className="text-sm text-gray-600">
                      Didn't receive the email?
                    </p>
                    
                    {resendCount > 0 && (
                      <p className="text-xs text-gray-500">
                        Email sent {resendCount} time{resendCount > 1 ? 's' : ''}
                      </p>
                    )}

                    <Button
                      variant="outline"
                      onClick={handleResendEmail}
                      disabled={isResending || timeLeft > 0}
                      className="w-full"
                    >
                      {isResending ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : timeLeft > 0 ? (
                        <>
                          <Clock className="mr-2 h-4 w-4" />
                          Resend in {formatTime(timeLeft)}
                        </>
                      ) : (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Resend email
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {!email && !error && (
              <div className="text-center space-y-4">
                <p className="text-gray-600">
                  A sign-in link has been sent to your email address.
                </p>
                <p className="text-sm text-gray-500">
                  Please check your inbox and click the link to continue.
                </p>
              </div>
            )}

            {/* Back to sign in */}
            <div className="pt-4 border-t border-gray-100">
              <Button
                variant="ghost"
                className="w-full"
                asChild
              >
                <Link href="/auth/signin">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to sign in
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Help text */}
        <div className="text-center text-xs text-gray-500 space-y-2">
          <p>
            Having trouble? Check your spam folder or{" "}
            <Link href="/contact" className="text-protec-red hover:underline">
              contact support
            </Link>
          </p>
          <p>
            By signing in, you agree to our{" "}
            <Link href="/terms" className="underline hover:text-protec-red">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="underline hover:text-protec-red">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}